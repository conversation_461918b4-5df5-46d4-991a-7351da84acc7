import numpy as np
import warnings
from dataclasses import dataclass, field
from typing import Any, Dict, Iterator, List, Optional, Set, Union

@dataclass(frozen=True, repr=False)
class DataInstance:
    """
    An immutable dataclass holding data for a single instance.

    Uses @dataclass(frozen=True) for immutability and provides both
    attribute-style (`instance.param_a`) and dictionary-style
    (`instance['param_a']`) read access.
    """
    _data: Dict[str, Any] = field(init=False, repr=False)

    def __init__(self, **kwargs: Any):
        """Initializes the instance with key-value data."""
        object.__setattr__(self, '_data', kwargs)

    def __getattr__(self, name: str) -> Any:
        """Enables attribute-style read access (e.g., `instance.key`)."""
        try:
            return self._data[name]
        except KeyError:
            raise AttributeError(f"'DataInstance' object has no attribute '{name}'")

    def __getitem__(self, key: str) -> Any:
        """Enables dictionary-style read access (e.g., `instance['key']`)."""
        return self._data[key]

    def __repr__(self) -> str:
        """Provides a clean string representation."""
        return f"DataInstance({self._data})"

    def __len__(self) -> int:
        """Returns the number of parameters."""
        return len(self._data)

    def __iter__(self) -> Iterator[str]:
        """Allows iteration over parameter keys."""
        return iter(self._data)

    def keys(self):
        """Returns the parameter keys."""
        return self._data.keys()

    def values(self):
        """Returns the parameter values."""
        return self._data.values()

    def items(self):
        """Returns the parameter key-value pairs."""
        return self._data.items()

class Dataset:
    """A container for a collection of DataInstance objects."""
    instances: List[DataInstance]
    parameters: Set[str]
    _fix_integrity: bool = True

    def __init__(self, instances: Optional[List[DataInstance]] = None):
        """
        Initializes the Dataset.

        Args:
            instances: An optional list of DataInstance objects.
        """
        self.instances = instances if instances is not None else []
        if self.instances:
            self.parameters = set().union(*(d.keys() for d in self.instances))
            self._verify_integrity()
        else:
            self.parameters = set()

    def _verify_integrity(self):
        """
        Ensures all instances have the same set of parameters.

        Under fix_integrity mode, if an instance is missing a parameter found in others,
        it is replaced by a new instance with a value of numpy.nan.
        """
        new_instances = []
        for instance in self.instances:
            instance_keys = instance.keys()
            if self.parameters != instance_keys:
                missing_keys = self.parameters - instance_keys
                if missing_keys:
                    warnings.warn(f"Instance missing keys: {missing_keys}. Filling with NaN.")
                    # Create a new dictionary with all current data
                    new_data = dict(instance.items())
                    # Add missing keys with nan
                    for key in missing_keys:
                        new_data[key] = np.nan
                    # Replace the old instance with a new, complete one
                    new_instances.append(DataInstance(**new_data))
                else:
                    new_instances.append(instance)
            else:
                new_instances.append(instance)
        self.instances = new_instances


    def append(self, instance: DataInstance):
        """Appends a single instance and updates parameters."""
        self.instances.append(instance)
        new_params = instance.keys() - self.parameters
        if new_params:
            self.parameters.update(new_params)
            self._verify_integrity()

    def extend(self, instances: List[DataInstance]):
        """Extends with a list of instances and updates parameters."""
        if not instances:
            return
        self.instances.extend(instances)
        # Find if there are any new parameters to trigger integrity check
        new_params = set().union(*(d.keys() for d in instances)) - self.parameters
        if new_params:
            self.parameters.update(new_params)
            self._verify_integrity()

    def add(self, data: Union[DataInstance, List[DataInstance]]):
        """Adds a single instance or a list of instances."""
        if isinstance(data, DataInstance):
            self.append(data)
        elif isinstance(data, list):
            self.extend(data)
        else:
            raise TypeError(f"Expected DataInstance or list of DataInstance, got {type(data)}")

    def __repr__(self) -> str:
        """Provides a summary of the dataset."""
        num_instances = len(self.instances)
        num_params = len(self.parameters)
        param_str = f"{num_params} parameters"
        instance_str = f"{num_instances} instances"

        header = f"<Dataset: {instance_str}, {param_str}>"
        if num_instances == 0:
            return header

        # Show first 5 instances as examples
        examples = "\nExamples:\n"
        for i, instance in enumerate(self.instances[:5]):
            examples += f"  [{i}] {instance}\n"
        return header + examples

    def get_param(self, key: str) -> List[Any]:
        """Gets all values for a single parameter, returns a list."""
        if key not in self.parameters:
            raise KeyError(f"Parameter '{key}' not found in dataset.")
        return [instance.get(key, np.nan) for instance in self.instances]

    def get(self, *keys: str) -> np.ndarray:
        """
        Gets data for multiple parameters as a NumPy array.

        Args:
            *keys: The parameter names to retrieve.

        Returns:
            A NumPy array where each row is an instance and each
            column corresponds to a key.
        """
        return np.array([self.get_param(key) for key in keys]).T

# --- DEMONSTRATION ---
if __name__ == "__main__":
    print("--- 1. Creating individual DataInstances ---")
    d1 = DataInstance(param_a=1, param_b=10.5, category='A')
    d2 = DataInstance(param_a=2, param_b=12.1, category='A')
    # This instance is missing 'param_b' and has an extra 'param_c'
    d3 = DataInstance(param_a=3, category='B', param_c=100)
    print(d1)
    print(d2)
    print(d3)

    print("\n--- 2. Creating a Dataset ---")
    # The integrity check will be triggered here
    dataset = Dataset([d1, d2, d3])
    print(dataset)
    print(f"All parameters in dataset: {sorted(list(dataset.parameters))}")

    print("\n--- 3. Adding new data ---")
    d4 = DataInstance(param_a=4, param_b=15.0, category='B', param_c=101)
    dataset.add(d4)
    print(dataset)

    print("\n--- 4. Retrieving data ---")
    # Get a single parameter as a list
    categories = dataset.get_param('category')
    print(f"Categories: {categories}")

    # Get multiple parameters as a NumPy array
    numerical_data = dataset.get('param_a', 'param_b')
    print("Numerical data (param_a, param_b):\n", numerical_data)

    # Note that d3's 'param_b' is now np.nan
    assert np.isnan(numerical_data[2, 1])
    print("\nAssertion successful: Missing value was correctly filled with NaN.")
